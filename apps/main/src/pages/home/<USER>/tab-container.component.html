<div class="top-box">
  <div class="top-left">
    <nz-input-group nzCompact [nzAddOnBefore]="addOnBeforeSelect" [nzSuffix]="suffixIcon">
      <input
        type="text"
        nz-input
        [(ngModel)]="searchValue"
        [maxlength]="50"
        [placeholder]="'dj-请输入解决方案名称' | translate"
        (ngModelChange)="handleClearSearch()"
      />
    </nz-input-group>
    <ng-template #addOnBeforeSelect>
      <ad-select
        [(ngModel)]="searchAppType"
        [nzAllowClear]="false"
        class="op-search-select"
        [disabled]="isTenantActive"
        (ngModelChange)="handleClearSearch()"
      >
        <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-数据驱动1.0' | translate" nzValue="1"></ad-option>
        <ad-option [nzLabel]="'dj-数据驱动2.0' | translate" nzValue="5"></ad-option>
        <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-随心控' | translate" nzValue="4"></ad-option>
        <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-敏捷问数1.0' | translate" nzValue="6"></ad-option>
        <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-敏捷问数2.0' | translate" nzValue="12"></ad-option>
        <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-数智员工' | translate" nzValue="7"></ad-option>
        <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-AI模型服务' | translate" nzValue="8"></ad-option>
        <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-高代码' | translate" nzValue="9"></ad-option>
        <!-- <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-数据可视化' | translate" nzValue="10"></ad-option> -->
        <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-业务领域中心' | translate" nzValue="11"></ad-option>
        <ad-option *ngIf="!isTenantActive" [nzLabel]="'dj-全部解决方案' | translate" nzValue=""></ad-option>
      </ad-select>
      <ad-select
        [disabled]="isTenantActive"
        [(ngModel)]="searchAppAuth"
        [nzAllowClear]="false"
        class="op-search-select auth-search-select"
        *ngIf="currentTab === 'recentVisited'"
        (ngModelChange)="handleClearSearch()"
      >
        <ad-option [nzLabel]="'dj-可管理' | translate" nzValue="application:mgr"></ad-option>
        <ad-option [nzLabel]="'dj-可协作' | translate" nzValue="application:actor"></ad-option>
        <ad-option [nzLabel]="'dj-全部权限' | translate" nzValue=""></ad-option>
      </ad-select>
    </ng-template>
    <ng-template #suffixIcon>
      <span class="input-clear" *ngIf="!!searchValue">
        <i adIcon type="close-circle" theme="fill" (click)="searchValue = ''; handleClearSearch()"></i>
      </span>
      <i adIcon iconfont="iconinputserach" class="searchIcon iconfont" aria-hidden="true" (click)="handleClearSearch()">
      </i>
    </ng-template>
  </div>
  <div class="top-right">
    <a [routerLink]="['/apps']">{{ 'dj-解决方案中心' | translate }} &gt;</a>
  </div>
</div>

<nz-spin [nzSpinning]="loading" class="spin-container">
  <div class="empty-wrap" *ngIf="!appList?.length">
    <ad-empty [nzNotFoundContent]="contentTpl" nzSize="large">
      <ng-template #contentTpl>
        <span class="tips">{{ 'dj-暂无解决方案' | translate }}，{{ 'dj-前往' | translate }}</span>
        <a [routerLink]="'/apps'"> {{ currentLanguage === 'en_US' ? ' ' : '' }}{{ 'dj-解决方案中心' | translate }}</a>
      </ng-template>
    </ad-empty>
  </div>
  <ng-container *ngIf="appList?.length">
    <!-- <app-solution-table
      [hidden]="switchValue !== 'list'"
      [data]="appList"
      [enableLoadMore]="false"
      [queryPermission]="handleQueryPermission"
      (handleAction)="handleGridAppAction($event)"
    >
    </app-solution-table> -->

    <div class="table-wrapper" [hidden]="switchValue !== 'list'">
      <app-solution-antd-table
        class="apps-table"
        [data]="appList"
        [optionTemplate]="optionTemplate"
        [containerRef]="containerRef"
        (appClick)="handleAppClick($event)"
      ></app-solution-antd-table>
      <ng-template #optionTemplate let-rowData>
        <i
          class="op-icon"
          adIcon
          iconfont="iconbianji1"
          nz-tooltip
          [nzTooltipTitle]="'dj-进入解决方案' | translate"
          (click)="handleAppClick(rowData)"
        ></i>
        <i
          adIcon
          class="op-icon"
          nz-tooltip
          [nzTooltipPlacement]="'topLeft'"
          [nzTooltipTitle]="'dj-发布' | translate"
          *ngIf="showHighCodeByAppTypes?.includes(rowData?.appType) && !hiddenMenuByEnv"
          [iconfont]="'icongaodaima1'"
          (click)="handleEnterHighApp(rowData)"
        ></i>
        <ng-container *ngIf="isManage(rowData.roles)">
          <i
            *ngIf="!isNana(rowData.appType)"
            class="op-icon font3"
            adIcon
            iconfont="iconshezhi123"
            nz-tooltip
            [nzTooltipTitle]="'dj-设置' | translate"
            (click)="handleSetApp(null, rowData)"
          ></i>
          <i
            *ngIf="isManageDelete(rowData.roles) && rowData.tag?.sourceComponent !== 'BC'"
            class="op-icon font4"
            adIcon
            iconfont="icondelete3"
            nz-tooltip
            [nzTooltipTitle]="'dj-删除' | translate"
            (click)="handleDeleteApp(null, rowData)"
          ></i>
        </ng-container>
      </ng-template>
    </div>

    <div [hidden]="switchValue !== 'card'" class="card-warp">
      <div *ngFor="let app of appList" class="app-card-container">
        <app-solution-card
          [data]="app"
          [showMore]="isManage(app.roles)"
          [loading]="statusMap.get(app.objectId)"
          [moreContentTemplate]="moreTemplate"
          (handleClick)="handleAppClick(app)"
          (enterHighApp)="handleEnterHighApp(app)"
        >
          <ng-template #moreTemplate>
            <div class="app-card-operate" style="cursor: pointer">
              <div *ngIf="!isNana(app.appType)" (click)="handleSetApp($event, app)">
                {{ 'dj-设置' | translate }}
              </div>
              <div
                *ngIf="isManageDelete(app.roles) && app.tag?.sourceComponent !== 'BC'"
                (click)="handleDeleteApp($event, app)"
              >
                {{ 'dj-删除' | translate }}
              </div>
            </div>
          </ng-template>
        </app-solution-card>
      </div>
    </div>
  </ng-container>
</nz-spin>

<app-delete-info
  [applyName]="appName"
  [applyNameArr]="applyNameArr"
  [appCode]="editingApp?.code"
  [appType]="editingApp?.appType"
  [isVisible]="deleteAppVisible"
  (ok)="handleAppOk($event)"
  (newOk)="handleDeleteNewOk($event)"
  (cancel)="handleDeleteCancel()"
>
</app-delete-info>

<app-info-edit
  *ngIf="appInfoVisible"
  [visible]="appInfoVisible"
  [formData]="editingApp"
  (callback)="handleAppEdit($event)"
></app-info-edit>
<app-create-agile-data2-app
  *ngIf="addAgileData2AppVisible"
  [visible]="addAgileData2AppVisible"
  opType="edit"
  [params]="{ appType: editingApp.appType }"
  [formData]="editingApp"
  (visibleChange)="addAgileData2AppVisible = $event"
  (afterCreated)="handleAppEdit($event)"
></app-create-agile-data2-app>
<app-create-app
  *ngIf="appV2InfoVisible"
  [visible]="appV2InfoVisible"
  opType="edit"
  type="empty"
  [params]="{ appType: editingApp?.appType }"
  [ignoreSuccessCallback]="true"
  [appCode]="editingApp?.code"
  (visibleChange)="onVisibleChange($event)"
  (afterCreated)="handleAppEdit($event)"
  (goApp)="handleGoApp($event)"
></app-create-app>

<ad-modal
  nzClassName="copy-modal"
  [nzWidth]="'432px'"
  [(nzVisible)]="copyModal"
  [nzTitle]="'dj-复制' | translate"
  [nzFooter]="null"
  [nzClosable]="true"
  [nzMaskClosable]="false"
  (nzOnCancel)="copyModal = false"
>
  <ng-container *adModalContent>
    <nz-spin [nzSpinning]="copyLoading">
      <p class="i-text-dark">{{ 'dj-复制解决方案' | translate }}</p>
      <form nz-form [formGroup]="copyForm" [nzNoColon]="true" class="form-info login-form">
        <nz-form-item>
          <nz-form-control [nzErrorTip]="'dj-请输入' | translate">
            <app-modal-input
              [attr]="{
                name: '名称',
                required: true,
                lang: appLang?.name,
                needLang: true
              }"
              [value]="appLang?.name?.[('dj-LANG' | translate)]"
              (callBack)="handlePatchApp('name', $event)"
              formControlName="name"
              ngDefaultControl
            ></app-modal-input>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-control [nzErrorTip]="userErrorTpl">
            <app-modal-input
              [attr]="{ name: '代号', required: true }"
              [value]="copyForm.get('code')?.value"
              (callBack)="handlePatchApp('code', $event)"
              formControlName="code"
              ngDefaultControl
            ></app-modal-input>
            <ng-template #userErrorTpl let-control>
              <ng-container *ngIf="control.hasError('required')">{{ 'dj-请输入' | translate }}</ng-container>
              <ng-container *ngIf="control.hasError('minlength')">{{ 'dj-最少2位字符' | translate }}</ng-container>
              <ng-container *ngIf="control.hasError('maxlength')">{{ 'dj-最多40位字符' | translate }}</ng-container>
              <ng-container *ngIf="control.hasError('duplicated')">{{
                (isInternalTenant(teamId) ? 'dj-app-regular4' : 'dj-app-regular3') | translate
              }}</ng-container>
            </ng-template>
          </nz-form-control>
        </nz-form-item>
      </form>
      <p>
        <font color="red">{{ 'dj-复制警示' | translate }}</font>
      </p>
      <div class="modal-footer">
        <button ad-button adType="default" (click)="copyModal = false">
          {{ 'dj-取消' | translate }}
        </button>
        <button ad-button adType="primary" (click)="handleCopy()">
          {{ 'dj-确定' | translate }}
        </button>
      </div>
    </nz-spin>
  </ng-container>
</ad-modal>

<!--下面的按钮不能直接显示图标，改变悬浮气泡文字选择-->
<!-- <ng-template #moreTemplate>
  <div class="app-card-operate" style="cursor: pointer">
    <div *ngIf="!isNanaAssistant">
      {{ 'dj-设置' | translate }}
    </div>
    <div *ngIf="isManageDelete(card.roles)">
      {{ 'dj-删除' | translate }}
    </div>
  </div>
</ng-template> -->

<!-- <section class="solution-list">
  <div class="solution-item">
    <div class="item-body">
      <div class="item-img">
        <div class="item-icon" style="background: linear-gradient(to right, rgb(255, 124, 208), rgb(255, 107, 122))">
          <img src="assets/img/app-icons/7.png" />
        </div>
      </div>
      <div class="item-info">
        <h3 class="title">TBB可视化解决方案</h3>
        <p class="code">代号：code_sdf_sdfsdf_sdfsdfsdf</p>
        <p class="desc">描述：解决方案描述文本</p>
      </div>
    </div>
    <div class="item-footer"></div>
  </div>
</section> -->
