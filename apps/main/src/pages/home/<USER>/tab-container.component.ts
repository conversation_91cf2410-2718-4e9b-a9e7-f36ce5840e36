import { Component, Input, OnInit, Output, EventEmitter, ViewChild, ElementRef } from '@angular/core';
import { AdUserService } from 'pages/login/service/user.service';
import { TranslateService } from '@ngx-translate/core';
import { HomeService } from '../service/home.service';
import { AppStatus, AppTypes } from 'pages/app/typings';
import { TenantService } from 'pages/login/service/tenant.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { isInternalTenant } from 'common/utils/core.utils';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import dayjs from 'dayjs';
import { Router } from '@angular/router';
import { AppService } from 'pages/apps/app.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { LocaleService } from 'common/service/locale.service';
import { AuthManageService } from 'components/bussiness-components/layout/menu-bar/auth-manage/auth-manage.service';
import { IHttpResp, IRecentAppModel } from '../service/type';
import { cloneDeep, debounce } from 'lodash';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { GlobalService } from 'common/service/global.service';
import { AuthPanelTypeEnum } from 'common/config/auth.config';
import { SystemConfigService } from 'common/service/system-config.service';
import { IndividualService } from 'pages/individual/individual.service';
import { CreateBlankComponent } from '../create-blank/create-blank.component';

@Component({
  selector: 'app-tab-container',
  templateUrl: './tab-container.component.html',
  styleUrls: ['./tab-container.component.less'],
})
export class TabContainerComponent implements OnInit {
  @Input() currentTab = 'recentCreated'; // recentCreated | recentVisited
  @Input() switchValue: 'card' | 'list';
  @Input('blankRef') createBlankRef: CreateBlankComponent;
  @Input() containerRef: ElementRef<HTMLElement>;

  @Output() switchValueChange = new EventEmitter<'card' | 'list'>();

  applyNameArr: Array<string>; // app多语言名称集合
  appName: string; // app名称

  // switchValue = 'card'; // card | list
  searchValue: string = '';
  searchAppType: string = '';
  searchAppAuth: string = '';
  isTenantActive: boolean;
  loading = true;
  appList: IRecentAppModel[] = [];
  timesInterval;
  destroy$ = new Subject();
  currentLanguage: string;
  /**
   * app过期的map
   */
  private appOvertimeMap: Map<string, string | null> = new Map();

  public rowData: any[];

  //
  public isInternalTenant = isInternalTenant;
  // 卡片的状态
  public statusMap: Map<string, boolean> = new Map();
  // 当前编辑的解决方案
  public editingApp?: IRecentAppModel = undefined;
  // 删除解决方案的确认框
  public deleteAppVisible: boolean = false;
  // 模型驱动解决方案修改弹框
  public appV2InfoVisible: boolean = false;
  // 其他的解决方案修改弹框
  public appInfoVisible: boolean = false;
  // 敏数2.0 修改弹框
  public addAgileData2AppVisible: boolean = false;
  // 复制解决方案
  public copyModal: boolean = false;
  // 是否正在复制
  public copyLoading: boolean = false;
  // lang
  public appLang: any = undefined;
  // 复制解决方案的表单
  public copyForm: FormGroup | undefined = undefined;

  isHighApp = AppTypes.HIGH_CODE; // 是否为高级应用

  hiddenMenuByEnv: boolean = false; //某些环境需要隐藏菜单，读取配置项
  showHighCodeByAppTypes: number[] = []; //某些制定的应用展示高代码入口

  constructor(
    private modal: AdModalService,
    private userService: AdUserService,
    private t: TranslateService,
    private homeService: HomeService,
    private router: Router,
    private appService: AppService,
    private message: NzMessageService,
    private translate: TranslateService,
    private tenantService: TenantService,
    private language: LocaleService,
    private authManageService: AuthManageService,
    private fb: FormBuilder,
    private globalService: GlobalService,
    private languageService: LocaleService,
    private configService: SystemConfigService,
    public individualService: IndividualService,
  ) {
    this.currentLanguage = this.language.currentLanguage;
    this.isTenantActive = this.userService.getUser('isTenantActive');
    this.homeService.appRefresh$.pipe(takeUntil(this.destroy$)).subscribe((data) => {
      if (data) {
        this.handleInit();
      }
    });
    this.configService.getConfig().subscribe((config: any) => {
      this.hiddenMenuByEnv = config.hiddenMenuByEnv === 'true';
      this.showHighCodeByAppTypes = config.showHighCodeByAppTypes?.split('_')?.map(Number);
    });
  }

  ngOnInit() {
    this.handleInit();
  }

  // 初始化数据
  async handleInit(): Promise<void> {
    this.loading = true;
    try {
      const res: IHttpResp<IRecentAppModel[]> = await this.homeService
        .queryHomeAppList({
          currentKey: this.currentTab,
          appType: this.searchAppType,
          condition: this.searchValue,
          searchAppAuth: this.searchAppAuth,
        })
        .toPromise();
      this.appList = (res?.data ?? []).map((item) => {
        item.appType = item.appType ?? AppTypes.DTD;
        return item;
      });
      this.queryAppOvertimeIfNeed();
      this.handleDeleting(this.appList);
    } catch {
      this.appList = [];
    } finally {
      this.loading = false;
    }
  }

  /**
   * 当是体验用户的时候，查询解决方案的过期时间
   */
  private async queryAppOvertimeIfNeed(): Promise<void> {
    if (!this.tenantService.isEduAndisExperience()) return;
    const codes = this.appList.map((app) => app.code).filter((code) => !this.appOvertimeMap.has(code));
    if (!codes.length) return;
    const result = await this.homeService.queryExperienceOverTime(codes).toPromise();
    if (result.code === 0) {
      result.data.forEach((item) => {
        this.appOvertimeMap.set(item.code, item.expiredTime || null);
      });
    }
  }

  /**
   * 处理删除中解决方案轮询
   * @param appList
   */
  handleDeleting(appList: any[]): void {
    if (this.timesInterval) clearInterval(this.timesInterval);
    if (appList.some((item) => item.deleteStatus === AppStatus.DELETING)) {
      this.timesInterval = setInterval(async () => {
        try {
          const res: IHttpResp<IRecentAppModel[]> = await this.homeService
            .queryHomeAppList({ num: 4, currentKey: this.currentTab })
            .toPromise();
          this.appList = (res?.data ?? []).map((item) => {
            item.appType = item.appType ?? AppTypes.DTD;
            return item;
          });
          if (!this.appList.some((e) => e.deleteStatus === AppStatus.DELETING)) {
            clearInterval(this.timesInterval);
          }
        } catch {
          clearInterval(this.timesInterval);
        }
      }, 3000);
    }
  }

  //#region 删除APP

  /**
   * 确认删除解决方案
   * @param event
   * @param app
   */
  public handleDeleteApp(event: MouseEvent | undefined, app: IRecentAppModel): void {
    event?.stopPropagation();
    event?.preventDefault();
    this.editingApp = app;
    const nameObj = app?.lang?.name;
    this.applyNameArr = [];
    this.appName = nameObj[this.languageService.currentLanguage];
    Object.entries(nameObj).forEach(([, value]) => {
      this.applyNameArr.push(value);
    });
    this.deleteAppVisible = true;
  }

  public async handleAppOk(value?: string): Promise<void> {
    this.statusMap.set(this.editingApp!.objectId, true);
    this.deleteAppVisible = false;
    try {
      const res = await this.homeService.deleteApp(this.editingApp!.code).toPromise();
      if (res.code === 0) {
        if (this.appService.selectedApp?.code && this.appService.selectedApp?.code === this.editingApp?.code) {
          this.appService.selectedApp = null;
        }
        this.message.success(this.t.instant('dj-home-删除成功'));
        this.handleInit();
      }
    } catch {
      this.message.error(this.t.instant('dj-home-请重试'));
    } finally {
      this.statusMap.set(this.editingApp!.objectId, false);
      this.handleDeleteCancel();
    }
  }

  public handleDeleteNewOk(status: string): void {
    if (status === 'success') {
      if (this.appService.selectedApp?.code && this.appService.selectedApp?.code === this.editingApp?.code) {
        this.appService.selectedApp = null;
      }
    }
    this.handleInit();
  }

  public handleDeleteCancel(): void {
    this.editingApp = undefined;
    this.deleteAppVisible = false;
  }

  //#endregion

  //#region 打开解决方案

  async handleAppClick(app: IRecentAppModel): Promise<void> {
    if (await this.checkAppBeforeOpen(app)) {
      this.handleSelectApp(app);
    }
  }

  /**
   * 检测解决方案是否可以打开
   * @param app
   * @returns true: 可以打开 false:不可以打开
   */
  private async checkAppBeforeOpen(app: IRecentAppModel): Promise<boolean> {
    if (this.statusMap.get(app.objectId)) return false;
    if (app.deleteStatus === AppStatus.DELETING) return false;
    if (app.deleteStatus === AppStatus.DELETE_FAIL) {
      this.message.warning(this.t.instant('dj-解决方案删除失败，请重新删除'));
      return false;
    }
    try {
      const res = await this.appService.queryAppInfo([app.code]);
      if (res.code === 0) {
        const info = res.data?.[0];
        if (info) {
          if (!info.deleteStatus || info.deleteStatus === AppStatus.SUCCESS) {
            return true;
          }
        }
      }
      return false;
    } catch {
      return false;
    }
  }

  // 选择APP
  private handleSelectApp(app: IRecentAppModel, data?: any): void {
    const isTenantActive = this.userService.getUser('isTenantActive');
    const { appType, code } = app;
    if (this.checkCodeIsOvertime(code)) {
      this.message.error(this.translate.instant('dj-当前解决方案体验已到期'));
      return;
    }
    this.appService.setAppAuthUser({ code: app.code }).subscribe(
      () => {},
      () => {},
    );
    // 新开页面
    switch (appType) {
      case AppTypes.COLLECT_DATA_V2: {
        // 数据收集型解决方案暂不维护，跳404
        this.router.navigateByUrl('errors/404');
        break;
      }
      case AppTypes.SCENARIO_KIT: {
        let urlString = data?.hightCode ? 'app/app-publish' : 'app/kit/task-control';
        const url = this.router.serializeUrl(
          this.router.createUrlTree([urlString], { queryParams: { appCode: app.code } }),
        );
        window.open(url, '_blank');
        break;
      }
      case AppTypes.NANA_ASSISTANT: {
        let urlString = 'asa-designer-web';
        let params: any = { appCode: app.code };
        if (data?.hightCode) {
          params.extensionApp = 'highCode';
          urlString = 'app';
        }
        const url = this.router.serializeUrl(this.router.createUrlTree([urlString], { queryParams: params }));
        window.open(url, '_blank');
        break;
      }
      case AppTypes.DATA_VIEW:
        // tbb解决方案,获取注册信息->拿到第一个menu后再跳转
        const designerInfo = this.globalService.standaloneDesigners.find((s) => s.appType === appType);
        // 处理hash路由
        const indexUrl = designerInfo.designer.routeMode === 'hash' ? '#/' : '';
        const url = decodeURIComponent(
          this.router.serializeUrl(
            this.router.createUrlTree([`standalone-solution${indexUrl}`], { queryParams: { appCode: app.code } }),
          ),
        );
        window.open(url, '_blank');
        break;
      case AppTypes.BUSINESS_DOMAIN_CENTER: {
        let urlString = 'app/business-domain-center/model-design';
        let params: any = { appCode: app.code };
        if (data?.hightCode) {
          params.extensionApp = 'highCode';
          urlString = 'app';
        }
        const url = this.router.serializeUrl(this.router.createUrlTree([urlString], { queryParams: params }));
        window.open(url, '_blank');
        break;
      }
      case AppTypes.HIGH_CODE: {
        const url = this.router.serializeUrl(
          this.router.createUrlTree(['app'], { queryParams: { appCode: app.code } }),
        );
        window.open(url, '_blank');
        break;
      }
      default: {
        let urlString = data?.hightCode ? 'app/app-publish' : 'app';
        const url = this.router.serializeUrl(
          this.router.createUrlTree([urlString], { queryParams: { appCode: app.code } }),
        );
        window.open(url, '_blank');
        break;
      }
    }
  }

  //#endregion

  //#region 设置解决方案

  /**
   * 设置解决方案信息
   * @param event
   * @param app
   */
  public async handleSetApp(event: MouseEvent | undefined, app: IRecentAppModel): Promise<void> {
    event?.stopPropagation();
    event?.preventDefault();

    this.editingApp = cloneDeep(app);
    if (!(await this.checkAppBeforeOpen(app))) {
      return;
    }
    if (this.editingApp.appType === AppTypes.MODEL_DRIVEN || this.editingApp.appType === AppTypes.HIGH_CODE) {
      this.appV2InfoVisible = true;
    } else if (this.editingApp.appType === AppTypes.AGILE_QUESTIONS) {
      this.addAgileData2AppVisible = true;
    } else {
      this.appInfoVisible = true;
    }
  }

  public handleAppEdit(app: Partial<IRecentAppModel>): void {
    if (app) {
      const index = this.appList.findIndex((item) => item.code === app.code);
      if (index > -1) {
        this.appList = this.appList.map((e, i) => {
          return i === index
            ? Object.assign({}, this.appList[index], app, { lang: app.application?.lang || app.lang })
            : e;
        });
        if (this.appService.selectedApp?.code && this.appService.selectedApp?.code === app.code) {
          this.appService.selectedApp = this.appList[index];
        }
      }
    }
    this.appInfoVisible = false;
    this.addAgileData2AppVisible = false;
    this.appV2InfoVisible = false;
    this.editingApp = undefined;
  }

  public onVisibleChange(visible: boolean): void {
    this.appV2InfoVisible = visible;
    if (!visible) {
      this.editingApp = undefined;
    }
  }

  handleGoApp(appCode) {
    const url = this.router.serializeUrl(this.router.createUrlTree(['app'], { queryParams: { appCode } }));
    window.open(url, '_blank');
  }

  //#endregion

  //#region 复制解决方案

  /**
   * 复制敏捷解决方案
   * @param event
   * @param app
   */
  public handleCopyApp(event: MouseEvent, app: IRecentAppModel): void {
    event.stopPropagation();
    event.preventDefault();
    const modalInstance = this.modal.confirm({
      nzTitle: null,
      nzContent: this.t.instant('dj-确定复制解决方案吗？', {
        n: app.name,
      }),
      nzMaskClosable: false,
      nzOkLoading: this.statusMap.get(app.objectId),
      nzBodyStyle: {
        height: '160px',
      },
      nzOnOk: async () => {
        try {
          this.statusMap.set(app.objectId, true);
          const param = { application: app.code };
          const res = await this.appService.copyAgileDataApp(param).toPromise();
          if (res?.code === 0) {
            this.message.success(this.t.instant('dj-复制成功'));
            modalInstance.destroy();
            this.handleInit();
          } else {
            this.message.error(this.t.instant('dj-home-请重试'));
          }
        } catch (error) {
          this.statusMap.set(app.objectId, false);
          this.message.error(this.t.instant('dj-home-请重试'));
        }
      },
      nzOnCancel: () => {},
    });
  }

  public async handleCopy(): Promise<void> {
    for (const i of Object.keys(this.copyForm?.controls)) {
      this.copyForm.controls[i].markAsDirty();
      this.copyForm.controls[i].updateValueAndValidity();
    }
    if (this.copyForm.valid) {
      this.copyLoading = true;
      const code = this.copyForm.get('code').value;
      const param = {
        targetApplication: this.editingApp?.code,
        application: {
          code: code,
          name: this.appLang?.name?.zh_CN,
          lang: this.appLang,
        },
      };
      try {
        const res = await this.appService.copyApp(param).toPromise();
        if (res.code === 0) {
          this.message.success(this.t.instant('dj-复制成功'));
          this.copyModal = false;
          this.handleInit();
        }
      } finally {
        this.editingApp = undefined;
        this.copyLoading = false;
      }
    }
  }

  /**
   * 复制解决方案
   * @param event
   * @param app
   */
  public handleOpenCopy(event: MouseEvent, app: IRecentAppModel): void {
    event.preventDefault();
    event.stopPropagation();
    this.editingApp = cloneDeep(app);
    this.appLang = {
      name: {
        // 复制名称
        en_US: app?.lang?.name?.en_US + '_copy',
        zh_CN: app?.lang?.name?.zh_CN + '_copy',
        zh_TW: app?.lang?.name?.zh_TW + '_copy',
      },
    };
    this.copyForm = this.fb.group({
      name: [this.appLang?.name?.zh_CN, [Validators.required]],
      code: [
        app.code + 'new-' + this.userService.getUser('tenantId'),
        [Validators.required, Validators.minLength(2), Validators.maxLength(40)],
        [this.appService.codeValidator],
      ],
    });
    this.copyModal = true;
  }

  public handlePatchApp(key: any, data: any): void {
    this.copyForm.patchValue({ [key]: data?.value });
    if (data.needLang) {
      this.appLang = {
        ...(this.appLang || {}),
        [key]: data.lang,
      };
    }
  }

  //#endregion

  public handleQueryPermission = (type: 'Delete' | 'Setting', app: IRecentAppModel): boolean => {
    if (type === 'Setting') {
      return !this.isNana(app.appType);
    }
    if (type === 'Delete') {
      return this.isManageDelete(app.roles);
    }
  };

  public handleGridAppAction(params: { type: 'Edit' | 'HighCode' | 'Setting' | 'Delete'; app: IRecentAppModel }): void {
    const { type, app } = params;
    switch (type) {
      case 'Edit':
        this.handleAppClick(app);
        break;
      case 'HighCode':
        // todo 进入高代码
        break;
      case 'Delete':
        this.handleDeleteApp(undefined, app);
        break;
      case 'Setting':
        this.handleSetApp(undefined, app);
        break;
    }
  }

  /**
   * 执行搜索
   */
  public handleClearSearch = debounce(
    () => {
      this.handleInit();
    },
    200,
    { leading: false, trailing: true },
  );

  /**
   * 检测解决方案有没有过期
   * @param code
   * @returns
   */
  private checkCodeIsOvertime(code: string) {
    if (this.appOvertimeMap.has(code)) {
      const overtime = this.appOvertimeMap.get(code);
      return overtime !== null && dayjs(overtime).isBefore(new Date());
    }
  }

  //#region html method
  public isNana(appType: AppTypes): boolean {
    return appType === AppTypes.NANA_ASSISTANT;
  }

  public isManageDelete(roles) {
    return this.authManageService.getDeleteAuth(roles);
  }

  handleSwitch(value: 'card' | 'list'): void {
    this.switchValueChange.emit(value);
  }

  isManage(roles) {
    return (
      this.authManageService.getAuthPanelType(roles) &&
      this.authManageService.getAuthPanelType(roles) !== AuthPanelTypeEnum.CooperateCommon
    );
  }

  /**
   * 点击进入高代码
   */
  async handleEnterHighApp(data) {
    if (await this.checkAppBeforeOpen(data)) {
      this.handleSelectApp(data, {
        hightCode: true,
      });
    }
  }
}
