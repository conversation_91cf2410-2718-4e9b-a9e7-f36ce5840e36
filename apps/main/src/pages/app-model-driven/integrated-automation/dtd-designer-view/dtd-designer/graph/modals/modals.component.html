<span class="update-tips" *ngIf="isGraphChanged">
  <div class="light"></div>
  {{ 'dj-已修改，注意保存' | translate }}
</span>

<!-- 添加任务的下拉 -->
<task-item-menu
  *ngIf="taskItemMenuVisible"
  [position]="taskItemMenuPosition"
  (select)="handleAddTask($event)"
></task-item-menu>

<!-- 节点右击菜单 -->
<!-- <graph-node-menu
  *ngIf="graphNodeMenuVisible"
  [isFromTemplate]="!!applicationCodeProxy"
  [position]="graphNodeMenuPosition"
  [node]="graphNodeMenuNode"
  [data]="graphNodeMenuData"
  (edit)="handleMenuEdit($event)"
  (delete)="handleMenuDelete($event)"
  (flow)="handleMenuFlow($event)"
  (apiFlow)="handleMenuApiFlow($event)"
  (pageDesign)="handleMenuPageDesign($event)"
  (mobilePageDesign)="handleMenuMobilePageDesign($event)"
></graph-node-menu> -->

<!-- 状态弹窗 -->
<ad-modal
  [(nzVisible)]="dataStateModalVisible"
  [nzWidth]="'580px'"
  [nzTitle]="'dj-设置' | translate"
  (nzOnCancel)="dataStateModalVisible = false"
  [nzFooter]="null"
>
  <ng-container *adModalContent>
    <app-dtd-status
      #dtdDataStatus
      [statusObj]="dataStateModalData"
      [featureList]="dataStateModalFeatureList"
      [favouriteCode]="favouriteCode"
      [applicationCodeProxy]="applicationCodeProxy"
    ></app-dtd-status>
    <div *ngIf="globalService.hollowPromise" class="modal-footer">
      <button ad-button adType="default" (click)="dataStateModalVisible = false">
        {{ 'dj-取消' | translate }}
      </button>
      <ng-container *operateAuth="{ prefix: 'update', templateSignal: !globalService.hollowPromise }">
        <button ad-button adType="primary" (click)="handleStateSave()" [nzLoading]="btnLoading">
          {{ 'dj-确定' | translate }}
        </button>
      </ng-container>
    </div>
  </ng-container>
</ad-modal>

<!-- 新建新T -->
<app-flow-widget
  *ngIf="newTModalVisible"
  [visible]="newTModalVisible"
  [favouriteCode]="favouriteCode"
  [applicationCodeProxy]="applicationCodeProxy"
  (afterSave)="handleAfterSaveModalSave($event)"
  (cancel)="newTModalVisible = false"
>
</app-flow-widget>

<!--任务开窗-->
<app-task-widget
  *ngIf="taskModalVisible"
  [visible]="taskModalVisible"
  [modalFlag]="modalFlag"
  [currentTaskData]="currentTaskData"
  [addedTask]="addedTask"
  [favouriteCode]="favouriteCode"
  [applicationCodeProxy]="applicationCodeProxy"
  (afterSave)="handleAfterSaveModalSave($event)"
  (cancel)="taskModalVisible = false"
>
</app-task-widget>

<!--特殊类型的任务界面设计：表单-->
<app-task-form-work-design
  *ngIf="formWorkVisible"
  [workVisible]="formWorkVisible"
  [workData]="workData"
  [adpVersionHeaders]="dtdDesignerService.adpVersionHeaders"
  [favouriteCode]="favouriteCode"
  [applicationCodeProxy]="applicationCodeProxy"
  (close)="handleFormPageClose()"
>
</app-task-form-work-design>

<!--任务界面设计-->
<app-task-work-design
  *ngIf="
    workVisible && workType === 0 && workData?.data?._pageModel === 'pageView' && workDesignType === 'taskOnlyDetail'
  "
  [workVisible]="workVisible"
  [workData]="workData"
  [favouriteCode]="favouriteCode"
  [applicationCodeProxy]="applicationCodeProxy"
  (close)="handlePageClose()"
>
</app-task-work-design>

<!-- 新的卡面设计器 -->
<app-task-project-card-design
  *ngIf="workVisible && workType === 0 && !isMobile && workDesignType === 'taskOnlyCard'"
  [workVisible]="workVisible"
  [workData]="workData"
  [type]="'taskOnlyCard'"
  [uiKey]="uiKey"
  [favouriteCode]="favouriteCode"
  [applicationCodeProxy]="applicationCodeProxy"
  (close)="handlePageClose()"
></app-task-project-card-design>

<!-- 新的任务界面设计器 -->
<app-task-work-design-new
  *ngIf="
    workVisible &&
    workType === 0 &&
    workData?.data?._pageModel === 'dsl' &&
    !isMobile &&
    workDesignType === 'taskOnlyDetail'
  "
  [workVisible]="workVisible"
  [workData]="workData"
  [type]="'taskOnlyDetail'"
  [uiKey]="uiKey"
  [favouriteCode]="favouriteCode"
  [applicationCodeProxy]="applicationCodeProxy"
  (close)="handlePageClose()"
></app-task-work-design-new>

<!--三合一新的任务界面设计器-->
<app-task-work-design-new-mobile
  *ngIf="workVisible && workType === 0 && isMobile"
  [workVisible]="workVisible"
  [workData]="workData"
  [favouriteCode]="favouriteCode"
  [applicationCodeProxy]="applicationCodeProxy"
  (close)="handlePageClose()"
></app-task-work-design-new-mobile>

<!--手工发起项目界面设计器-->
<app-project-work-design
  *ngIf="workVisible && workType === 1 && workData?.data?._pageModel === 'pageView'"
  [workVisible]="workVisible"
  [workData]="workData"
  [descriptionLang]="workData?.descriptionLang"
  [uiKey]="uiKey"
  [favouriteCode]="favouriteCode"
  [applicationCodeProxy]="applicationCodeProxy"
  (close)="handlePageClose()"
>
</app-project-work-design>

<!-- 新的手工发起项目界面设计器 -->
<app-project-work-design-new
  *ngIf="workVisible && workType === 1 && workData?.data?._pageModel === 'dsl' && !isMobile"
  [workVisible]="workVisible"
  [workData]="workData"
  [descriptionLang]="workData?.descriptionLang"
  [favouriteCode]="favouriteCode"
  [applicationCodeProxy]="applicationCodeProxy"
  (close)="handlePageClose()"
>
</app-project-work-design-new>

<app-project-work-design-new-mobile
  *ngIf="workVisible && workType === 1 && workData?.data?._pageModel === 'dsl' && isMobile"
  [workVisible]="workVisible"
  [workData]="workData"
  [descriptionLang]="workData?.descriptionLang"
  (close)="handlePageClose()"
>
</app-project-work-design-new-mobile>

<!--原始界面设计-->
<app-work-design
  *ngIf="workVisible && workType === 2"
  [workVisible]="workVisible"
  [workData]="workData"
  [favouriteCode]="favouriteCode"
  [applicationCodeProxy]="applicationCodeProxy"
  (close)="handlePageClose()"
>
</app-work-design>

<!-- API设计弹窗 -->
<task-api-design-modal
  [selectedTask]="apiDesignTask"
  [extendHeader]="taskVersion"
  [isFromDtdReference]="apiDesignTask?.isFromDtdReference"
  [(visible)]="apiDesignModalVisible"
  [favouriteCode]="favouriteCode"
  [applicationCodeProxy]="applicationCodeProxy"
  *ngIf="apiDesignModalVisible"
></task-api-design-modal>

<!-- 关联数据 -->
<ad-modal
  nzClassName="task-copy-modal"
  [(nzVisible)]="associatedDataModalVisible"
  [nzTitle]="associatedDataModalTitle"
  [nzWidth]="'470px'"
  [nzClosable]="true"
  [nzCentered]="false"
  [nzOkLoading]="associatedDataLoading"
  (nzOnCancel)="handleAssociatedDataCancel()"
  (nzOnOk)="handleAssociatedDataSave()"
  [nzMaskClosable]="false"
>
  <ng-container *adModalContent>
    <app-associated-data
      #associatedData
      [activeStateCodes]="associatedDataActiveStateCodes"
      [type]="associatedDataModalType"
      [favouriteCode]="favouriteCode"
      [applicationCodeProxy]="applicationCodeProxy"
      (updateStates)="handleAssociatedDataUpdateStates()"
    >
    </app-associated-data>
  </ng-container>
</ad-modal>

<!-- 关联任务弹框 -->
<ad-modal
  nzClassName="task-copy-modal"
  [(nzVisible)]="associatedTaskModalVisible"
  [nzTitle]="associatedTaskModalTitle"
  [nzWidth]="'470px'"
  [nzClosable]="true"
  [nzCentered]="false"
  [nzOkLoading]="associatedTaskLoading"
  (nzOnOk)="handleAssociatedTaskSave()"
  (nzOnCancel)="handleAssociatedTaskCancel()"
  [nzMaskClosable]="false"
>
  <ng-container *adModalContent>
    <app-associated-task
      #associatedTask
      [type]="associatedTaskModalType"
      [activeTasks]="associatedTaskActiveTasks"
      [favouriteCode]="favouriteCode"
      [applicationCodeProxy]="applicationCodeProxy"
      (updateTask)="handleAssociatedTaskUpdateTask()"
    ></app-associated-task>
  </ng-container>
</ad-modal>

<task-flow
  [visible]="flowVisible"
  [task]="flowTask"
  [extendHeader]="taskVersion"
  [favouriteCode]="favouriteCode"
  [isFromDtdReference]="flowTask?.isFromDtdReference"
  [applicationCodeProxy]="applicationCodeProxy"
  (flowDrawerClose)="handleFlowDrawerClose()"
></task-flow>
