import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { fromEvent, Subject, Subscription } from 'rxjs';
import { ViewApiService } from '../../../service/api.service';
import { isJSON, validatorForm } from 'common/utils/core.utils';
import { cloneDeep, debounce, throttle } from 'lodash';
import { takeUntil } from 'rxjs/operators';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { scripts } from 'pages/app/dtd/activity-flow/service/flow.util';
import { responseScript } from 'pages/app/dtd/drive-execution-new/components/api-design/service/script-template';
import { ViewStoreService } from '../../../service/store.service';
import { kDomainList } from '../../../config/utils';

enum EActionType {
  ESP = 'ESP',
  HTTP = 'HTTP',
  Script = 'SCRIPT',
  PASS = 'SKIP',
  Modify = 'MODEL_UPDATE',
  AUTOCOMPLETE = 'AUTOCOMPLETE',
}

@Component({
  selector: 'app-return-strategy',
  templateUrl: './return-strategy.component.html',
  styleUrls: ['./return-strategy.component.less'],
})
export class ReturnStrategyComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() returnStrategyData: any[];
  @Input() nodeId: string;
  // 前面的人工节点
  @Input() preManualNodes;

  @Output() returnStrategyDataChange = new EventEmitter();

  EActionType = EActionType;
  loading: boolean = false;
  form: FormGroup;

  @ViewChild('scrollContent') scrollContent: ElementRef<HTMLDivElement>;

  // 获取返回策略
  get returnStrategy() {
    // 获取表单中的返回策略
    return this.form?.get('returnStrategy') as FormArray;
  }

  get projectCatetory() {
    return this.viewStoreService.state.projectCategory;
  }

  private subjectList: {
    [key: string]: Subscription;
  }[];
  private destory$ = new Subject();
  private formGroupValidityFlag: boolean = false;
  private visibleMap = new Map<string, boolean>();

  private scrollSubject: Subscription;

  //#region 脚本编辑器
  // 脚本编辑器
  scriptModal: boolean = false;
  scriptData: string | null = null;

  editingForm:
    | {
        index: number;
        from?: string;
      }
    | undefined = undefined;
  //#endregion

  // 下拉选项
  actionOptions: { label: string; value: string }[] = [
    {
      label: 'ESP',
      value: EActionType.ESP,
    },
    {
      label: 'HTTP',
      value: EActionType.HTTP,
    },
    {
      label: this.translate.instant('dj-脚本'),
      value: EActionType.Script,
    },
    {
      label: this.translate.instant('dj-修改模型审核状态'),
      value: EActionType.Modify,
    },
    {
      label: this.translate.instant('dj-跳过'),
      value: EActionType.PASS,
    },
    {
      label: this.translate.instant('dj-自动完成'),
      value: EActionType.AUTOCOMPLETE,
    },
  ];
  NodeOptions = [];

  //#region esp
  // action开窗是否显示
  espActionVisible: boolean = false;

  produces: {
    [k: string]: string[];
  } = {};
  //#endregion

  //#region http
  readonly domainList: string[] = kDomainList;
  //#endregion

  //#region 更新模型
  modelList: {
    label: string;
    value: string;
  }[] = [];

  modelStatus: {
    [k: string]: any[];
  } = {};

  get taskCode() {
    if (this.viewStoreService.isFusionMode()) return this.viewStoreService.state?.originalFlowData?.taskCode;
    return undefined;
  }

  //#endregion

  constructor(
    private fb: FormBuilder,
    private translate: TranslateService,
    private modal: AdModalService,
    private apiService: ViewApiService,
    private viewStoreService: ViewStoreService,
  ) {
    this.initForm();
  }

  async ngOnInit(): Promise<void> {
    try {
      this.loading = true;
      await this.getModelVaribale();
      this.handleInit();
      this.getNodeOptions();
      this.getCurrentData();
    } finally {
      this.loading = false;
    }
  }

  ngAfterViewInit(): void {
    this.scrollSubject = fromEvent(this.scrollContent.nativeElement, 'scroll').subscribe(
      throttle(
        () => {
          this.visibleMap.forEach((v, k) => {
            if (v) {
              this.visibleMap.set(k, false);
            }
          });
          this.visibleMap.clear();
        },
        100,
        { leading: true, trailing: false },
      ),
    );
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('nodeId')) {
      // id不相同认为是不同的节点需要重新构建formgroup，后期可以使用patchValue,而非重新初始化formgroup
      if (!changes.nodeId.firstChange) {
        this.formGroupValidityFlag = true;
        this.handleInit();
        this.getNodeOptions();
        this.formGroupValidityFlag = false;
        this.getCurrentData();
      }
    }
  }

  ngOnDestroy(): void {
    this.scrollSubject?.unsubscribe();
    this.unSubject();
    this.destory$.next();
    this.destory$.complete();
  }

  // 初始化form
  initForm(): void {
    this.form = this.fb.group({
      returnStrategy: this.fb.array([]),
    });
    this.form.valueChanges.pipe(takeUntil(this.destory$)).subscribe((data) => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
  }

  /**
   * 新增
   */
  addAction(): void {
    this.returnStrategy?.push(
      this.fb.group({
        nodeId: ['', [Validators.required]],
        // nodeType: [''],
        expand: [true],
        exceptionPolicy: [null],
        actionCfg: this.fb.group({
          domain: [''],
          url: [''],
          prod: [''],
          method: [''],
          isAsync: [false],
          serviceName: [''],
          header: [this.objectToString({})],
          requestScript: [''],
          responseScript: [''],

          // 更新模型状态
          bindForm: this.fb.group({
            modelCode: [null],
            serviceCode: [null],
          }),
          fieldInfos: this.fb.array([
            this.fb.group({
              fieldId: ['manage_status'],
              fieldValue: [null],
              fieldType: ['VARCHAR'],
              fullPath: [''],
              paramType: ['constant'],
            }),
          ]),
        }),
        adpType: [null, [Validators.required]],
      }),
    );
    this.subscriptActionChange();
  }

  drop(event: CdkDragDrop<string[]>): void {
    moveItemInArray(this.returnStrategy?.controls, event.previousIndex, event.currentIndex);
    moveItemInArray(this.returnStrategy?.value, event.previousIndex, event.currentIndex);
    this.getCurrentData();
  }

  /**
   * 删除动作
   * @param index
   */
  removeAction(index: number): void {
    this.modal.confirm({
      nzTitle: this.translate.instant('dj-确认删除？'),
      nzWrapClassName: 'vertical-center-modal',
      nzOkText: this.translate.instant('dj-确定'),
      nzOnOk: () => {
        this.returnStrategy.removeAt(index);
        this.subscriptActionChange();
      },
      nzOnCancel: () => {},
    });
  }

  /**
   * 展开/收起面板
   * @param e
   * @param index
   */
  toggleExpand(e: MouseEvent, index: number): void {
    e.stopPropagation();
    this.returnStrategy.at(index).patchValue({ expand: !this.returnStrategy.at(index).value.expand });
  }

  //#region 模型修改
  async handleModelChange(index: number, value: string): Promise<void> {
    const config = this.returnStrategy.at(index).get('actionCfg');
    const serviceCode = config.get('bindForm').get('serviceCode').value;
    const obj: { [k: string]: any } = {};
    if (serviceCode) {
      const [modelCode] = serviceCode.split('&');
      obj['fullPath'] = `${modelCode}.manage_status`;
    } else {
      obj['fullPath'] = null;
    }
    const fieldInfos = config.get('fieldInfos') as FormArray;
    fieldInfos.at(0).patchValue({
      ...obj,
      fieldValue: null,
    });
    if (serviceCode) {
      await this.getModelStatus(serviceCode);
    }
  }

  getDictionaries(path?: string): any[] {
    return this.modelStatus[path] || [];
  }

  //#endregion

  //#region esp methods

  /**
   * 显示ESP动作选择面板
   * @param index
   */
  showEspAction(index: number): void {
    this.editingForm = {
      index,
    };
    this.espActionVisible = true;
  }

  /**
   * 选择ESP动作面的回掉
   * @param data
   */
  async handleSelectAction(data: any, close: boolean): Promise<void> {
    if (!close && data.actionId) {
      const actionId = data.actionId.startsWith('esp_') ? data.actionId.replace('esp_', '') : data.actionId;
      const { index } = this.editingForm;
      this.returnStrategy.at(index).get('actionCfg').get('serviceName').setValue(actionId);
      this.espActionVisible = false;
      await this.queryProducts(actionId);
      this.handleSelect(index, this.produces[actionId][0]);
    } else {
      this.espActionVisible = false;
    }
    this.editingForm = undefined;
  }

  handleSelect(index: number, item: string): void {
    this.returnStrategy.at(index).get('actionCfg').get('prod').setValue(item);
  }

  showScriptModal(index: number, from: 'requestScript' | 'responseScript'): void {
    const defaultScript = this.returnStrategy.at(index).get('actionCfg').get(from).value;
    this.editingForm = { index, from: 'actionCfg.' + from };
    if (defaultScript) {
      this.scriptData = defaultScript;
    } else {
      const adpType = this.returnStrategy.at(index).get('adpType').value;
      this.scriptData = this.getDefaultScript(adpType, from === 'requestScript');
    }
    this.scriptModal = true;
  }
  //#endregion

  //#region 脚本编辑回显
  handleCloseSript(action: 'confirm' | 'close', data: string): void {
    if (action === 'confirm') {
      const { index, from } = this.editingForm;
      const activeForm = this.returnStrategy;
      if (from) {
        const path = from.split('.');
        let control = activeForm.at(index);
        while (path.length) {
          const first = path.shift();
          control = control.get(first);
        }
        control.setValue(data);
      }
    }
    this.scriptModal = false;
    this.scriptData = undefined;
    this.editingForm = undefined;
  }

  handleDropMenuVisible(visible: boolean, index: number): void {
    this.visibleMap.set(`${index}`, visible);
  }

  //#endregion

  //#region private methods

  /**
   * 获取产品名
   * @param actionId
   */
  private async queryProducts(actionId: string): Promise<void> {
    try {
      if (!this.produces[actionId]) {
        const res = (await this.apiService.getApiProvider(actionId).toPromise()) as any;
        this.produces[actionId] = res.data || [];
      }
    } catch {}
  }

  private cleanForm(): void {
    this.returnStrategy?.clear();
  }

  private handleInit(): void {
    this.cleanForm();
    this.returnStrategyData?.forEach((item) => {
      const fieldInfos = item.actionCfg?.fieldInfos;
      const hasValue = this.modelList.find(
        (e) => e.value === `${item.actionCfg?.bindForm?.modelCode}&${item.actionCfg?.bindForm?.serviceCode}`,
      );
      this.returnStrategy?.push(
        this.fb.group({
          nodeId: [item.nodeId || null, [Validators.required]],
          // nodeType: [item.nodeType || null],
          expand: true,
          exceptionPolicy: item.exceptionPolicy || null,
          adpType: [item.adpType || null, [Validators.required]],
          actionCfg: this.fb.group({
            domain: item.actionCfg?.domain || '',
            url: item.actionCfg?.url || '',
            prod: item.actionCfg?.prod || '',
            method: item.actionCfg?.method || '',
            isAsync: item.actionCfg?.isAsync || false,
            serviceName: item.actionCfg?.serviceName || '',
            header: this.objectToString(item.actionCfg?.header || {}),
            requestScript: item.actionCfg?.requestScript || '',
            responseScript: item.actionCfg?.responseScript || '',

            bindForm: this.fb.group({
              modelCode: hasValue ? item.actionCfg?.bindForm?.modelCode : null,
              serviceCode: hasValue ? hasValue.value : null,
            }),
            fieldInfos: this.fb.array(
              // 兼容老版空数组[]的情况
              fieldInfos && fieldInfos.length
                ? fieldInfos?.map((info) =>
                    this.fb.group({
                      fieldId: info?.fieldId || null,
                      fieldValue: info?.fieldValue || null,
                      fieldType: info?.fieldType || null,
                      fullPath: info?.fullPath || null,
                      paramType: info?.paramType || null,
                    }),
                  )
                : [
                    this.fb.group({
                      fieldId: 'manage_status',
                      fieldValue: null,
                      fieldType: 'VARCHAR',
                      fullPath: '',
                      paramType: 'constant',
                    }),
                  ],
            ),
          }),
        }),
      );
      if (item.adpType === EActionType.ESP && item.actionCfg?.serviceName) {
        this.queryProducts(item.actionCfg?.serviceName);
      }
    });
    this.subscriptActionChange();
  }

  async getNodeOptions() {
    let parentPreManualNodes = [];
    if (this.taskCode) {
      const res = await this.apiService.queryBeforeNodes(this.taskCode).toPromise();
      parentPreManualNodes = res?.data?.beforeTaskNodes?.map((item) => {
        return {
          nodeName: item.name[this.translate.instant('dj-LANG')] || item.name,
          id: item.code,
        };
      });
    }
    this.NodeOptions = [
      {
        label: this.translate.instant('dj-项目开始'),
        value: 'projectStart',
        optionTip: this.translate.instant('dj-勾选后支持退回至项目开始'),
        groupLabel: this.translate.instant('dj-开始节点'),
      },
      this.projectCatetory === 'combined' && {
        label: this.translate.instant('dj-任务开始'),
        value: 'taskStart',
        optionTip: this.translate.instant('dj-勾选后支持退回至当前大T的开始'),
        groupLabel: this.translate.instant('dj-开始节点'),
      },
      ...this.preManualNodes.map((e) => ({
        label: e.nodeName,
        value: e.id,
        groupLabel: this.translate.instant('dj-当前任务'),
      })),
      ...parentPreManualNodes.map((e) => ({
        label: e.nodeName,
        value: e.id,
        groupLabel: this.translate.instant('dj-其他节点'),
      })),
    ].filter(Boolean);
  }

  /**
   * 获取模型变量
   */
  private async getModelVaribale(): Promise<void> {
    try {
      const res = await this.apiService.getBusinessDirList().toPromise();
      const _modelList: any[] = [];
      res.data.forEach((item) => {
        const models = item.businessDirTree.find((e) => e.type === 'modelDesign')?.businessDirTree || [];
        models.forEach((model) => {
          _modelList.push({
            modelCode: model.businessSubCode,
            serviceCode: model.serviceCode,
            groupLabel: item.isOther ? this.translate.instant('dj-其他') : undefined,
          });
        });
      });
      await this.getModelFields(_modelList);
    } catch {
      // empty
    }
  }

  /**
   * 筛选有manage_status的模型
   * @param params
   */
  private async getModelFields(params: any[]): Promise<void> {
    const map = params.reduce((pre, curr) => {
      pre[`${curr.modelCode}&${curr.serviceCode}`] = curr;
      return pre;
    }, {});
    const data = await this.queryModelByCodes(params);
    const models = data
      .filter((e) => e.model.fields.some((f) => f.fieldId === 'manage_status'))
      .map((e) => {
        const value = `${e.code}&${e.serviceCode}`;
        this.handleOneModel(e.model, value);
        return {
          label: e.lang.name[this.translate.instant('dj-LANG')] || e.name,
          groupLabel: map[value].groupLabel,
          modelCode: e.code,
          serviceCode: e.serviceCode,
          value: value,
        };
      });
    this.modelList = models;
  }

  /**
   * 设置监听
   */
  private subscriptActionChange(): void {
    this.unSubject();
    const subscribes = this.returnStrategy.controls.map((control, i) => {
      const actionCfg = this.returnStrategy.at(i).get('actionCfg');
      const setValidators = (value) => {
        const actionCfg = this.returnStrategy.at(i).get('actionCfg');
        if (value === EActionType.Modify) {
          actionCfg.get('bindForm').get('serviceCode').setValidators(Validators.required);
        } else {
          actionCfg.get('bindForm').get('serviceCode').setValidators(null);
        }
        const fieldInfoForm = actionCfg.get('fieldInfos') as FormArray;
        fieldInfoForm.controls.forEach((control) => {
          control.get('fieldValue').setValidators(value === EActionType.Modify ? Validators.required : null);
        });
      };
      setValidators(control.get('adpType').value);
      const adpTypeSubscribe = control.get('adpType').valueChanges.subscribe((value) => {
        setValidators(value);
        actionCfg.patchValue({
          type: null,
          domain: null,
          url: '',
          prod: '',
          method: '',
          isAsync: false,
          serviceName: '',
          header: this.objectToString({}),
          requestScript: '',
          responseScript: '',
          bindForm: {
            serviceCode: null,
          },
          fieldInfos: [
            {
              fieldId: 'manage_status',
              fieldValue: null,
              fieldType: 'VARCHAR',
              fullPath: '',
              paramType: 'constant',
            },
          ],
        });
      });

      // const nodeIdSubscribe = control.get('nodeId').valueChanges.subscribe((value) => {
      //   const nodeType = this.NodeOptions?.find((e) => e.nodeId === value)?.nodeType;
      //   if (nodeType) {
      //     control.patchValue({
      //       nodeType,
      //     });
      //   }
      // });
      return {
        adpTypeSubscribe,
        // nodeIdSubscribe,
      };
    });
    this.subjectList = subscribes;
  }

  /**
   * 取消监听
   */
  private unSubject(): void {
    this.subjectList?.forEach((subject) => {
      subject.adpTypeSubscribe?.unsubscribe();
      subject.nodeIdSubscribe?.unsubscribe();
    });
    this.subjectList = [];
  }

  /**
   * 获取当前数据
   */
  private getCurrentData = debounce(
    () => {
      this.formGroupValidityFlag = true;
      validatorForm(this.form);
      this.formGroupValidityFlag = false;
      const values = this.form.getRawValue();
      this.handleFormData(cloneDeep(values));
    },
    100,
    { leading: false, trailing: true },
  );

  private handleFormData(values: any): void {
    values['returnStrategy']?.forEach((item) => {
      item.actionType = item.adpType;
      item.actionCfg.type = item.adpType;
      if (item.actionCfg.header) {
        try {
          item.actionCfg.header = JSON.parse(item.actionCfg.header);
        } catch {}
      }
      if (item.actionCfg?.bindForm?.serviceCode) {
        const [modelCode, serviceCode] = item.actionCfg?.bindForm?.serviceCode.split('&');
        item.actionCfg.bindForm = {
          modelCode,
          serviceCode,
        };
      }
    });
    this.returnStrategyDataChange.emit(values?.returnStrategy || []);
  }

  /**
   * 批量查询模型
   * @param models
   */
  private async queryModelByCodes(models: { modelCode: string; serviceCode: string }[]): Promise<any> {
    const res = await this.apiService.queryModelByCodes(models).toPromise();
    return res.data || [];
  }

  /**
   * 获取模型的ManageStatus的下拉词汇
   * @param fullCode
   */
  private async getModelStatus(fullCode: string): Promise<void> {
    if (!this.modelStatus[fullCode]) {
      const [modelCode, serviceCode] = fullCode.split('&');
      const res = await this.apiService.fetchModelDrivenData({ code: modelCode, serviceCode: serviceCode }).toPromise();
      if (res.code === 0) {
        this.handleOneModel(res.data.model, fullCode);
      }
    }
  }

  private handleOneModel(model: any, fullCode: string): void {
    const dictionaryContent = model.fields.find((e) => e.fieldId === 'manage_status')?.dictionaryContent;
    if (dictionaryContent) {
      const dictionary = JSON.parse(dictionaryContent);
      this.modelStatus[fullCode] = dictionary;
    }
  }

  /**
   * 获取默认的脚本
   * @param type
   * @param isReq
   * @returns
   */
  private getDefaultScript(type: EActionType, isReq: boolean = false): string {
    if (type === EActionType.ESP) {
      if (isReq) return scripts.script_5;
      return responseScript(JSON.stringify({}, null, '\t'));
    }
    if (type === EActionType.HTTP) {
      if (isReq) {
        return `/*
脚本处理请求参数后return内容为接口入参
  var request = {
      'std_data': {
          'parameter': {
          }
      }
  };
  return request;
 */`;
      }
      return `/*
处理返回结果并存储
var response = $(response);
  return {
    "success" : true,
    "processVariable" : {
      "key1" : "value1"
    },
    "errorMessage" : ""
  };
 */`;
    }
    if (type === EActionType.Script) {
      return `/*
进行数据处理，转换，更新
例 拿到当前节点输出中的name更新流程变量name
return{
  "name":$variables['ServiceTask_ec333f586c982d2b019cc8a42a061506']['name'] //当前节点id
}
 */`;
    }
    return undefined;
  }

  private objectToString(obj: any = {}): string {
    if (typeof obj === 'string') return obj;
    return JSON.stringify(obj);
  }
  //#endregion
}
